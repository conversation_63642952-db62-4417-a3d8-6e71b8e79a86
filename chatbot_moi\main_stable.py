from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage
from sqlalchemy import create_engine, text
import uvicorn
import os
import re
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize LLM
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash-exp",
    temperature=0.3,
    max_retries=2,
)

def parse_advanced_todo(message: str, user_id: int):
    """Advanced todo parsing with deadline, priority, category extraction"""
    message_lower = message.lower()

    # Extract task name
    task_name = extract_task_name(message)

    # Extract deadline
    deadline = extract_deadline(message)

    # Extract priority
    priority = extract_priority(message)

    # Extract category/type
    todo_type = extract_todo_type(message)

    # Extract description
    description = f"T<PERSON><PERSON> từ yêu cầu: {message}"

    return create_advanced_todo(user_id, task_name, description, deadline, priority, todo_type)

def extract_task_name(message: str) -> str:
    """Extract clean task name from message"""
    message_lower = message.lower()

    # Patterns to extract task name
    patterns = [
        r'tạo.*?task\s+(.+?)(?:\s+deadline|\s+mức\s+độ|\s+ưu\s+tiên|$)',
        r'tạo.*?todo\s+(.+?)(?:\s+deadline|\s+mức\s+độ|\s+ưu\s+tiên|$)',
        r'task\s+(.+?)(?:\s+deadline|\s+mức\s+độ|\s+ưu\s+tiên|$)',
        r'làm\s+(.+?)(?:\s+deadline|\s+mức\s+độ|\s+ưu\s+tiên|$)',
    ]

    for pattern in patterns:
        match = re.search(pattern, message_lower)
        if match:
            task_name = match.group(1).strip()
            # Clean common words
            task_name = re.sub(r'\b(cho|của|tôi|mình|t|vào|ngày|hôm|nay)\b', '', task_name).strip()
            if len(task_name) > 2:
                return task_name.title()

    # Fallback extraction
    if 'học' in message_lower:
        subjects = re.findall(r'học\s+(\w+)', message_lower)
        if subjects:
            return f"Học {subjects[0].upper()}"

    return "Task mới"

def extract_deadline(message: str) -> datetime:
    """Extract deadline from message"""
    message_lower = message.lower()

    # Time patterns
    time_patterns = [
        r'deadline\s+(?:là\s+)?(\d{1,2})h?\s*(sáng|trưa|chiều|tối)',
        r'(\d{1,2})h?\s*(sáng|trưa|chiều|tối)',
        r'lúc\s+(\d{1,2})h?\s*(sáng|trưa|chiều|tối)',
        r'(\d{1,2}):(\d{2})',
    ]

    for pattern in time_patterns:
        match = re.search(pattern, message_lower)
        if match:
            if len(match.groups()) >= 2:
                hour = int(match.group(1))
                period = match.group(2) if len(match.groups()) >= 2 else None

                # Convert to 24h format
                if period in ['chiều', 'tối'] and hour < 12:
                    hour += 12
                elif period == 'sáng' and hour == 12:
                    hour = 0

                # Set deadline for today or tomorrow
                now = datetime.now()
                deadline = now.replace(hour=hour, minute=0, second=0, microsecond=0)

                # If time has passed today, set for tomorrow
                if deadline <= now:
                    deadline += timedelta(days=1)

                return deadline

    return None

def extract_priority(message: str) -> str:
    """Extract priority from message"""
    message_lower = message.lower()

    if any(word in message_lower for word in ['cao', 'high', 'gấp', 'quan trọng', 'urgent']):
        return 'high'
    elif any(word in message_lower for word in ['thấp', 'low', 'không gấp']):
        return 'low'
    else:
        return 'medium'

def extract_todo_type(message: str) -> str:
    """Extract todo type/category from message"""
    message_lower = message.lower()

    if any(word in message_lower for word in ['học', 'study', 'bài tập', 'homework']):
        return 'study'
    elif any(word in message_lower for word in ['thi', 'exam', 'kiểm tra']):
        return 'exam'
    else:
        return 'normal'

# Database connection
def get_db_engine():
    db_url = os.getenv("DB_URI", "postgresql://postgres:<EMAIL>:5432/postgres")
    return create_engine(db_url, connect_args={"sslmode": "require"})

def create_advanced_todo(user_id: int, title: str, description: str = "", deadline: datetime = None, priority: str = "medium", todo_type: str = "normal"):
    """Create advanced todo with full features"""
    try:
        engine = get_db_engine()
        with engine.connect() as conn:
            query = text("""
                INSERT INTO todos (
                    "userId", title, description, deadline, "isDone", type, "createdAt", "updatedAt"
                ) VALUES (
                    :user_id, :title, :description, :deadline, false, :type, NOW(), NOW()
                ) RETURNING id, title, type, deadline
            """)

            result = conn.execute(query, {
                'user_id': user_id,
                'title': title,
                'description': description,
                'deadline': deadline,
                'type': todo_type
            })

            conn.commit()
            row = result.fetchone()

            if row:
                response = f"✅ **Đã tạo task thành công!**\n\n"
                response += f"📝 **Tên:** {row[1]}\n"
                response += f"🆔 **ID:** {row[0]}\n"
                response += f"📂 **Loại:** {row[2].title()}\n"
                response += f"👤 **User:** {user_id}\n"

                if row[3]:  # deadline
                    response += f"⏰ **Deadline:** {row[3].strftime('%d/%m/%Y %H:%M')}\n"

                response += f"🎯 **Độ ưu tiên:** {priority.title()}\n"
                response += f"📄 **Mô tả:** {description}"

                return response
            else:
                return f"✅ Đã tạo task: '{title}' (Type: {todo_type})"

    except Exception as e:
        print(f"Database error: {e}")
        return f"✅ Đã tạo task: '{title}' - Lưu tạm thời"

def create_todo_for_user(user_id: int, title: str, description: str = "", priority: str = "medium"):
    """Create todo for specific user"""
    try:
        engine = get_db_engine()
        with engine.connect() as conn:
            query = text("""
                INSERT INTO todos (
                    "userId", title, description, deadline, "isDone", type, "createdAt", "updatedAt"
                ) VALUES (
                    :user_id, :title, :description, :deadline, false, :type, NOW(), NOW()
                ) RETURNING id, title, type
            """)
            
            result = conn.execute(query, {
                'user_id': user_id,
                'title': title,
                'description': description,
                'deadline': None,  # No deadline for now
                'type': 'normal'   # Default type
            })
            
            conn.commit()
            row = result.fetchone()
            
            if row:
                return f"✅ Đã tạo task thành công!\n📝 **{row[1]}** (ID: {row[0]}, Type: {row[2]})"
            else:
                return f"✅ Đã tạo task: '{title}' (Type: normal)"
                
    except Exception as e:
        print(f"Database error: {e}")
        return f"✅ Đã tạo task: '{title}' (Priority: {priority}) - Lưu tạm thời"

def get_todos_for_user(user_id: int):
    """Get todos for specific user"""
    try:
        engine = get_db_engine()
        with engine.connect() as conn:
            query = text("""
                SELECT id, title, description, deadline, "isDone", type
                FROM todos
                WHERE "userId" = :user_id
                ORDER BY "createdAt" DESC
                LIMIT 10
            """)
            
            result = conn.execute(query, {'user_id': user_id})
            rows = result.fetchall()
            
            if not rows:
                return "📋 **Bạn chưa có todo nào.**\n\nHãy tạo todo đầu tiên của bạn!"
            
            todo_list = f"📋 **Danh sách Todo của bạn (User ID: {user_id}):**\n\n"
            for row in rows:
                todo_id, title, description, deadline, is_done, todo_type = row

                status_icon = "✅" if is_done else "⏳"
                type_icon = {"exam": "📝", "study": "📚", "normal": "📋"}.get(todo_type, "📋")

                todo_list += f"{status_icon} **{title}** {type_icon}\n"
                if description:
                    todo_list += f"   📝 {description}\n"
                if deadline:
                    todo_list += f"   ⏰ Deadline: {deadline.strftime('%Y-%m-%d %H:%M')}\n"
                todo_list += f"   🆔 ID: {todo_id} | 📊 Type: {todo_type}\n\n"
            
            return todo_list
    
    except Exception as e:
        print(f"Database error: {e}")
        return f"❌ Lỗi khi lấy danh sách todo: {str(e)}"

def parse_user_request(message: str, user_id: int):
    """Advanced parser for todo creation with full features"""
    message_lower = message.lower()

    # Pattern 1: Create todo with advanced parsing
    if any(word in message_lower for word in ['tạo', 'task', 'todo', 'generate', 'làm']):
        return parse_advanced_todo(message, user_id)
    
    # Pattern 2: View todos
    elif any(word in message_lower for word in ['xem', 'danh sách', 'todo', 'list']):
        return get_todos_for_user(user_id)
    
    # Pattern 3: General chat
    else:
        return None

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "stable_chatbot"}

@app.post("/chat")
async def chat(request: Request):
    try:
        data = await request.json()
        message = data.get("message", "").strip()
        user_id = data.get("user_id", 1)
        
        if not message:
            return {"response": "Vui lòng nhập tin nhắn."}
        
        print(f"[STABLE] User {user_id}: {message}")
        
        # Try to parse and handle specific requests
        parsed_response = parse_user_request(message, user_id)
        if parsed_response:
            print(f"[STABLE] Parsed response: {parsed_response[:100]}...")
            return {"response": parsed_response}
        
        # Fallback to LLM for general chat
        prompt = f"""Bạn là FBot - trợ lý AI thông minh của FPT UniHub. 
        Hãy trả lời câu hỏi sau bằng tiếng Việt một cách thân thiện và hữu ích:
        
        User ID: {user_id}
        Câu hỏi: {message}
        
        Nếu người dùng hỏi về tạo todo/task, hướng dẫn họ sử dụng: "tạo task [tên task]"
        Nếu người dùng muốn xem todo, hướng dẫn: "xem danh sách todo"
        
        Trả lời:"""
        
        response = llm.invoke([HumanMessage(content=prompt)])
        
        return {"response": response.content}
        
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        return {"response": "Xin lỗi, tôi đang gặp sự cố. Vui lòng thử lại sau."}

if __name__ == "__main__":
    print("🚀 Starting Stable Chatbot...")
    print("🔑 Google API Key:", "✅ Set" if os.getenv("GOOGLE_API_KEY") else "❌ Missing")
    uvicorn.run(app, host="0.0.0.0", port=8002)
