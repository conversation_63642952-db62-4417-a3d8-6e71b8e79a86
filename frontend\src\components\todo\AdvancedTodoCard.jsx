import React, { useState } from 'react';
import { motion } from 'framer-motion';

const AdvancedTodoCard = ({ todo, onToggleComplete, onDelete, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(todo.title);
  const [editDescription, setEditDescription] = useState(todo.description || '');

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-green-500 bg-green-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'study': return '📚';
      case 'exam': return '📝';
      case 'normal': return '📋';
      default: return '📋';
    }
  };

  const formatDeadline = (deadline) => {
    if (!deadline) return null;
    const date = new Date(deadline);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return { text: 'Quá hạn', color: 'text-red-600 bg-red-100' };
    if (diffDays === 0) return { text: 'Hôm nay', color: 'text-orange-600 bg-orange-100' };
    if (diffDays === 1) return { text: 'Ngày mai', color: 'text-blue-600 bg-blue-100' };
    return { text: `${diffDays} ngày`, color: 'text-gray-600 bg-gray-100' };
  };

  const handleSave = () => {
    onUpdate(todo.id, { title: editTitle, description: editDescription });
    setIsEditing(false);
  };

  const deadlineInfo = formatDeadline(todo.deadline);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`
        relative p-6 rounded-xl border-l-4 shadow-lg hover:shadow-xl transition-all duration-300
        ${getPriorityColor(todo.priority)}
        ${todo.isDone ? 'opacity-60' : ''}
      `}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => onToggleComplete(todo)}
            className={`
              w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all
              ${todo.isDone 
                ? 'bg-green-500 border-green-500 text-white' 
                : 'border-gray-300 hover:border-green-400'
              }
            `}
          >
            {todo.isDone && '✓'}
          </button>
          
          <div className="flex items-center space-x-2">
            <span className="text-2xl">{getTypeIcon(todo.type)}</span>
            <span className="text-xs px-2 py-1 rounded-full bg-white/50 text-gray-600">
              ID: {todo.id}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {deadlineInfo && (
            <span className={`text-xs px-2 py-1 rounded-full ${deadlineInfo.color}`}>
              ⏰ {deadlineInfo.text}
            </span>
          )}
          
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="text-gray-400 hover:text-blue-500 transition-colors"
          >
            ✏️
          </button>
          
          <button
            onClick={() => onDelete(todo.id)}
            className="text-gray-400 hover:text-red-500 transition-colors"
          >
            🗑️
          </button>
        </div>
      </div>

      {/* Content */}
      {isEditing ? (
        <div className="space-y-3">
          <input
            value={editTitle}
            onChange={(e) => setEditTitle(e.target.value)}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="Tiêu đề task..."
          />
          <textarea
            value={editDescription}
            onChange={(e) => setEditDescription(e.target.value)}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            rows="3"
            placeholder="Mô tả..."
          />
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Lưu
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
            >
              Hủy
            </button>
          </div>
        </div>
      ) : (
        <div>
          <h3 className={`text-lg font-semibold mb-2 ${todo.isDone ? 'line-through text-gray-500' : 'text-gray-800'}`}>
            {todo.title}
          </h3>
          
          {todo.description && (
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
              {todo.description}
            </p>
          )}

          {/* Metadata */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <span>📅</span>
                <span>{new Date(todo.createdAt).toLocaleDateString('vi-VN')}</span>
              </span>
              
              {todo.deadline && (
                <span className="flex items-center space-x-1">
                  <span>⏰</span>
                  <span>{new Date(todo.deadline).toLocaleString('vi-VN')}</span>
                </span>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 rounded-full text-xs ${getPriorityColor(todo.priority)}`}>
                {todo.priority?.toUpperCase() || 'MEDIUM'}
              </span>
              
              <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600">
                {todo.type?.toUpperCase() || 'NORMAL'}
              </span>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default AdvancedTodoCard;
