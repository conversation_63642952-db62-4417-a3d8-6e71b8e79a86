#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Advanced Todo Management System...\n');

// Function to run command and log output
function runCommand(command, cwd = process.cwd()) {
  console.log(`📦 Running: ${command}`);
  try {
    execSync(command, { 
      cwd, 
      stdio: 'inherit',
      encoding: 'utf8'
    });
    console.log('✅ Success!\n');
  } catch (error) {
    console.error(`❌ Error running command: ${command}`);
    console.error(error.message);
    process.exit(1);
  }
}

// Check if we're in the right directory
if (!fs.existsSync('backend') || !fs.existsSync('frontend')) {
  console.error('❌ Please run this script from the project root directory');
  process.exit(1);
}

console.log('1️⃣ Installing backend dependencies...');
// Install backend dependencies
runCommand('npm install multer', path.join(process.cwd(), 'backend'));

console.log('2️⃣ Installing frontend dependencies...');
// Install frontend dependencies (try without problematic packages first)
runCommand('npm install lucide-react --legacy-peer-deps', path.join(process.cwd(), 'frontend'));

console.log('3️⃣ Creating uploads directory...');
// Create uploads directory
const uploadsDir = path.join(process.cwd(), 'backend', 'uploads', 'todos');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('✅ Uploads directory created\n');
} else {
  console.log('✅ Uploads directory already exists\n');
}

console.log('4️⃣ Running database migration...');
// Run migration
runCommand('node run-migration.js', path.join(process.cwd(), 'backend'));

console.log('5️⃣ Testing the system...');
// Test the system
runCommand('node test-advanced-todo.js', path.join(process.cwd(), 'backend'));

console.log('🎉 Setup completed successfully!\n');

console.log('📋 What\'s been implemented:');
console.log('✅ Enhanced Database Schema with 20+ new fields');
console.log('✅ Advanced Todo Form with all requested features');
console.log('✅ Multiple View Modes (Grid, List, Kanban, Calendar, Analytics)');
console.log('✅ Time Tracking System with start/stop timer');
console.log('✅ Progress Tracking (0-100%)');
console.log('✅ Teacher Assignment System');
console.log('✅ Comments System');
console.log('✅ File Attachments Support');
console.log('✅ Bulk Operations');
console.log('✅ Advanced Filtering & Search');
console.log('✅ Analytics Dashboard');

console.log('\n🚀 Next steps:');
console.log('1. Start the backend: cd backend && npm start');
console.log('2. Start the frontend: cd frontend && npm run dev');
console.log('3. Open http://localhost:5173 to see the new system');
console.log('4. Check the Teacher Dashboard if you have teacher role');

console.log('\n📚 Documentation:');
console.log('- Read ADVANCED_TODO_SYSTEM.md for detailed features');
console.log('- Check backend/test-advanced-todo.js for usage examples');

console.log('\n💡 Tips:');
console.log('- Use the new Advanced Todo Form to create feature-rich todos');
console.log('- Try different view modes: Grid, List, Kanban, Calendar, Analytics');
console.log('- Use the timer feature to track time spent on tasks');
console.log('- Teachers can assign homework to entire classrooms');
console.log('- Use bulk operations to manage multiple todos at once');

console.log('\n🎯 The system now rivals and exceeds Notion AI in todo management!');
