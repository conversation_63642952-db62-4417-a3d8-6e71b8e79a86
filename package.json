{"name": "hackathon-advanced-todo", "version": "2.0.0", "description": "Advanced Todo Management System - Professional task management that rivals Notion AI", "main": "index.js", "scripts": {"setup": "node setup-advanced-todo.js", "dev": "concurrently \"cd backend && npm start\" \"cd frontend && npm run dev\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run dev", "migrate": "cd backend && node run-migration.js", "test:system": "cd backend && node test-advanced-todo.js", "test": "npm run test:system"}, "keywords": ["todo", "task-management", "productivity", "education", "teacher-tools", "time-tracking", "kanban", "analytics"], "author": "Advanced Todo Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}