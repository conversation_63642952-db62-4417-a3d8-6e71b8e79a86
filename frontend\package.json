{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "autoprefixer": "^10.4.21", "axios": "^1.6.7", "date-fns": "^2.30.0", "framer-motion": "^12.19.1", "lucide-react": "^0.536.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^4.6.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^5.0.8"}}