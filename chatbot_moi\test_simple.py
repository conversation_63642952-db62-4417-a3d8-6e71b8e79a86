#!/usr/bin/env python3
"""
Simple test to identify chatbot issues
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_imports():
    """Test all imports"""
    print("🔍 Testing imports...")
    
    try:
        from fastapi import FastAPI
        print("✅ FastAPI imported")
    except Exception as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        from langchain_core.messages import HumanMessage
        print("✅ LangChain core imported")
    except Exception as e:
        print(f"❌ LangChain core import failed: {e}")
        return False
    
    try:
        from config.llm import llm
        print("✅ LLM config imported")
    except Exception as e:
        print(f"❌ LLM config import failed: {e}")
        return False
    
    try:
        from agents.tools import create_todo, get_todos
        print("✅ Basic tools imported")
    except Exception as e:
        print(f"❌ Basic tools import failed: {e}")
        return False
    
    try:
        from agents.tools import create_multiple_todos, create_learning_plan
        print("✅ New tools imported")
    except Exception as e:
        print(f"❌ New tools import failed: {e}")
        print("⚠️ Will use basic tools only")
    
    try:
        from agents.graph import multi_agent_graph
        print("✅ Multi-agent graph imported")
    except Exception as e:
        print(f"❌ Multi-agent graph import failed: {e}")
        return False
    
    return True

def test_environment():
    """Test environment variables"""
    print("\n🔍 Testing environment variables...")
    
    required_vars = ['GOOGLE_API_KEY', 'DB_URI', 'TAVILY_API_KEY']
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}...{value[-4:]}")
        else:
            print(f"❌ {var}: Not set")
            return False
    
    return True

def test_llm():
    """Test LLM connection"""
    print("\n🔍 Testing LLM connection...")
    
    try:
        from config.llm import llm
        from langchain_core.messages import HumanMessage
        
        response = llm.invoke([HumanMessage(content="Hello, say 'test successful'")])
        print(f"✅ LLM response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ LLM test failed: {e}")
        return False

def test_database():
    """Test database connection"""
    print("\n🔍 Testing database connection...")
    
    try:
        from sqlalchemy import create_engine, text
        
        db_url = os.getenv("DB_URI")
        engine = create_engine(db_url, connect_args={"sslmode": "require"})
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            print(f"✅ Database connection successful: {row[0]}")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_simple_chat():
    """Test simple chat functionality"""
    print("\n🔍 Testing simple chat...")
    
    try:
        from agents.graph import multi_agent_graph
        from langchain_core.messages import HumanMessage
        
        state = {"messages": [HumanMessage(content="xin chào")]}
        result = multi_agent_graph.invoke(state)
        
        response = result.get("response", "No response")
        print(f"✅ Chat test successful: {response}")
        return True
    except Exception as e:
        print(f"❌ Chat test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 CHATBOT DIAGNOSTIC TEST")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Environment Test", test_environment),
        ("LLM Test", test_llm),
        ("Database Test", test_database),
        ("Chat Test", test_simple_chat)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*50}")
    print(f"📊 RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Chatbot should work correctly.")
    else:
        print("⚠️ Some tests failed. Please fix the issues above.")

if __name__ == "__main__":
    main()
