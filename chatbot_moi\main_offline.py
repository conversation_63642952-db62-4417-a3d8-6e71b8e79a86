from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import re
from datetime import datetime, timedelta

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def parse_schedule_request(message):
    """Parse schedule creation requests"""
    message_lower = message.lower()
    
    # Check for learning plan requests
    if any(keyword in message_lower for keyword in ['lộ trình', 'học', 'tháng', 'python', 'javascript']):
        if 'python' in message_lower:
            return create_python_learning_plan(message)
        elif 'javascript' in message_lower:
            return create_js_learning_plan(message)
    
    # Check for multiple todo requests
    if ',' in message and any(time_word in message_lower for time_word in ['sáng', 'trưa', 'chiều', 'tối', 'h']):
        return parse_multiple_todos(message)
    
    # Check for single todo
    if any(keyword in message_lower for keyword in ['tạo', 'task', 'todo', 'công việc']):
        return create_single_todo(message)
    
    return None

def create_python_learning_plan(message):
    """Create Python learning plan"""
    duration = 3  # default
    if 'tháng' in message:
        numbers = re.findall(r'\d+', message)
        if numbers:
            duration = int(numbers[0])
    
    plan = f"""
🎯 **Lộ trình học Python trong {duration} tháng**

📚 **Kế hoạch chi tiết:**

**Tháng 1: Nền tảng cơ bản**
• Tuần 1-2: Cài đặt Python, biến, kiểu dữ liệu, toán tử
• Tuần 3-4: Cấu trúc điều khiển (if/else, loops), functions

**Tháng 2: Cấu trúc dữ liệu và OOP**
• Tuần 5-6: Lists, dictionaries, sets, file handling
• Tuần 7-8: Classes, objects, inheritance

**Tháng 3: Ứng dụng thực tế**
• Tuần 9-10: Web scraping, APIs, databases
• Tuần 11-12: Flask web framework, dự án cuối khóa

💡 **Lời khuyên:**
- Thực hành coding mỗi ngày 1-2 tiếng
- Làm dự án nhỏ sau mỗi chủ đề
- Tham gia cộng đồng Python Việt Nam

✅ **Đã tạo lộ trình học Python {duration} tháng thành công!**
    """
    return plan.strip()

def create_js_learning_plan(message):
    """Create JavaScript learning plan"""
    duration = 3  # default
    if 'tháng' in message:
        numbers = re.findall(r'\d+', message)
        if numbers:
            duration = int(numbers[0])
    
    plan = f"""
🎯 **Lộ trình học JavaScript trong {duration} tháng**

📚 **Kế hoạch chi tiết:**

**Tháng 1: Cơ bản**
• Tuần 1-2: HTML/CSS foundation, JS syntax, variables
• Tuần 3-4: Functions, arrays, objects, DOM manipulation

**Tháng 2: Nâng cao**
• Tuần 5-6: Events, forms, AJAX, APIs
• Tuần 7-8: ES6+, promises, async/await

**Tháng 3: Framework**
• Tuần 9-10: React hoặc Vue.js cơ bản
• Tuần 11-12: Dự án web app hoàn chỉnh

💡 **Lời khuyên:**
- Thực hành trên CodePen, JSFiddle
- Xây dựng portfolio trên GitHub
- Học cùng cộng đồng JavaScript VN

✅ **Đã tạo lộ trình học JavaScript {duration} tháng thành công!**
    """
    return plan.strip()

def parse_multiple_todos(message):
    """Parse multiple todo creation"""
    tasks = [task.strip() for task in message.split(',')]
    
    result = "✅ **Đã tạo thành công các task sau:**\n\n"
    
    for i, task in enumerate(tasks, 1):
        # Extract time if present
        time_match = re.search(r'(\d{1,2})h?\s*(sáng|trưa|chiều|tối)', task)
        if time_match:
            hour = int(time_match.group(1))
            period = time_match.group(2)
            
            if period == 'chiều' and hour < 12:
                hour += 12
            elif period == 'tối' and hour < 12:
                hour += 12
            
            # Clean task name
            clean_task = re.sub(r'\d{1,2}h?\s*(sáng|trưa|chiều|tối)', '', task).strip()
            clean_task = re.sub(r'thứ\s*\d+', '', clean_task).strip()
            
            result += f"• **Task {i}:** {clean_task}\n"
            result += f"  ⏰ Thời gian: {hour:02d}:00\n"
            result += f"  📅 Ngày: Thứ 2 tuần tới\n\n"
        else:
            result += f"• **Task {i}:** {task}\n"
            result += f"  📅 Ngày: Hôm nay\n\n"
    
    result += "💡 **Gợi ý:** Hãy chia nhỏ các task lớn thành các bước cụ thể để dễ thực hiện hơn!"
    
    return result

def create_single_todo(message):
    """Create single todo"""
    # Extract task name
    task_name = re.sub(r'(tạo|task|todo|công việc)', '', message, flags=re.IGNORECASE).strip()
    
    if not task_name:
        task_name = "Task mới"
    
    return f"""
✅ **Đã tạo task thành công!**

📝 **Tên task:** {task_name}
📅 **Ngày tạo:** {datetime.now().strftime('%d/%m/%Y %H:%M')}
⚡ **Độ ưu tiên:** Trung bình
📋 **Trạng thái:** Chưa hoàn thành

💡 **Gợi ý:** Bạn có thể thêm deadline và mô tả chi tiết để quản lý tốt hơn!
    """

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "offline_chatbot"}

@app.post("/chat")
async def chat(request: Request):
    try:
        data = await request.json()
        message = data.get("message", "").strip()
        
        if not message:
            return {"response": "Vui lòng nhập tin nhắn."}
        
        # Try to parse schedule/todo requests
        schedule_response = parse_schedule_request(message)
        if schedule_response:
            return {"response": schedule_response}
        
        # Default responses for common queries
        message_lower = message.lower()
        
        if any(greeting in message_lower for greeting in ['xin chào', 'hello', 'hi', 'chào']):
            return {"response": "Chào bạn! 👋 Tôi là FBot - trợ lý AI của FPT UniHub. Tôi có thể giúp bạn:\n\n• 📋 Tạo và quản lý todo list\n• 🎓 Lập lộ trình học tập\n• ⏰ Sắp xếp lịch trình\n\nBạn cần hỗ trợ gì không? 😊"}
        
        elif any(keyword in message_lower for keyword in ['học phí', 'tuition', 'fee']):
            return {"response": "💰 **Thông tin học phí FPT University:**\n\n• Công nghệ thông tin: ~25-30 triệu/năm\n• Kinh doanh quốc tế: ~20-25 triệu/năm\n• Marketing: ~20-25 triệu/năm\n\n📞 Liên hệ: (028) 7300 1866 để biết thêm chi tiết!"}
        
        elif any(keyword in message_lower for keyword in ['help', 'giúp', 'hướng dẫn']):
            return {"response": "🤖 **Hướng dẫn sử dụng FBot:**\n\n📋 **Tạo todo:**\n• 'tạo task học Python'\n• 'thứ 2 học toán 9h sáng, 12h trưa ăn trưa'\n\n🎓 **Lộ trình học:**\n• 'tạo lộ trình học Python 3 tháng'\n• 'lộ trình học JavaScript'\n\n❓ **Hỏi đáp:**\n• 'học phí FPT là bao nhiêu'\n• 'điều kiện tuyển sinh'\n\nHãy thử một trong những câu lệnh trên! 😊"}
        
        else:
            return {"response": f"Tôi hiểu bạn đang hỏi về: '{message}'\n\n🤔 Hiện tại tôi đang trong chế độ offline nên chưa thể trả lời câu hỏi này. \n\nTôi có thể giúp bạn:\n• 📋 Tạo todo list\n• 🎓 Lập lộ trình học\n• ⏰ Quản lý lịch trình\n\nHãy thử: 'tạo lộ trình học Python 3 tháng' hoặc 'help' để xem hướng dẫn! 😊"}
        
    except Exception as e:
        print(f"Error: {e}")
        return {"response": "Xin lỗi, tôi đang gặp sự cố. Vui lòng thử lại sau."}

if __name__ == "__main__":
    print("🚀 Starting Offline Chatbot...")
    print("💡 This version works without API keys!")
    uvicorn.run(app, host="0.0.0.0", port=8002)
