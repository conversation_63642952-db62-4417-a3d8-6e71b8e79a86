from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage
import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize LLM
llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.3,
    max_retries=2,
)

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "simple_chatbot"}

@app.post("/chat")
async def chat(request: Request):
    try:
        data = await request.json()
        message = data.get("message")
        
        if not message:
            return {"response": "<PERSON>ui lòng nhập tin nhắn."}
        
        # Simple prompt for Vietnamese responses
        prompt = f"""Bạn là FBot - trợ lý AI thông minh của FPT UniHub. 
        Hãy trả lời câu hỏi sau bằng tiếng Việt một cách thân thiện và hữu ích:
        
        Câu hỏi: {message}
        
        Nếu người dùng hỏi về:
        - Tạo todo/task: Hướng dẫn họ cách tạo và quản lý công việc
        - Lộ trình học: Đưa ra lời khuyên về cách học hiệu quả
        - Lịch trình: Giúp họ sắp xếp thời gian hợp lý
        
        Trả lời:"""
        
        # Get response from LLM
        response = llm.invoke([HumanMessage(content=prompt)])
        
        return {"response": response.content}
        
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        return {"response": "Xin lỗi, tôi đang gặp sự cố. Vui lòng thử lại sau."}

if __name__ == "__main__":
    print("🚀 Starting Simple Chatbot...")
    print("🔑 Google API Key:", "✅ Set" if os.getenv("GOOGLE_API_KEY") else "❌ Missing")
    uvicorn.run(app, host="0.0.0.0", port=8002)
