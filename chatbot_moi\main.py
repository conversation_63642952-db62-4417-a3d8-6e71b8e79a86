from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from agents.graph import multi_agent_graph
from agents.memory import chat_memory
from agents.tools import set_user_context
from langchain_core.messages import HumanMessage
import uvicorn

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "chatbot_moi"}

@app.post("/chat")
async def chat(request: Request):
    try:
        data = await request.json()
        message = data.get("message")
        user_id = data.get("user_id", "default")  # Get user ID if provided

        if not message:
            return {"response": "Vui lòng nhập tin nhắn."}

        # Extract and store user preferences
        chat_memory.extract_user_preferences(message, user_id)

        # Set user context for tools to use
        user_context = chat_memory.get_user_context(user_id)
        set_user_context(user_id, user_context)

        # Get user context for better responses
        context_summary = chat_memory.get_context_summary(user_id)

        # Enhanced message with context
        enhanced_message = f"""
User Message: {message}

User Context: {context_summary}

Current User ID: {user_id}

Please respond considering the user's context and preferences.
        """

        # Create state with user message and context
        state = {
            "messages": [HumanMessage(content=enhanced_message)],
            "user_context": chat_memory.get_user_context(user_id),
            "conversation_history": chat_memory.get_conversation_history(user_id),
            "current_user_id": user_id  # Add user ID to state
        }

        # Process through multi-agent graph
        result = multi_agent_graph.invoke(state)

        response = result.get("response", "Xin lỗi, tôi không thể trả lời lúc này.")

        # Store conversation in memory
        chat_memory.add_to_conversation_history(user_id, message, response)

        # Update user context if todos were created
        if "✅" in response and ("task" in response.lower() or "todo" in response.lower()):
            context = chat_memory.get_user_context(user_id)
            context['created_todos'].append({
                'message': message,
                'response': response,
                'timestamp': chat_memory.conversation_histories[user_id][-1]['timestamp']
            })
            chat_memory.update_user_context(user_id, **context)

        return {"response": response, "agentType": result.get("route_decision", "unknown")}

    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        return {"response": "Xin lỗi, tôi đang gặp sự cố. Vui lòng thử lại sau."}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "Chatbot service is running"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8002)