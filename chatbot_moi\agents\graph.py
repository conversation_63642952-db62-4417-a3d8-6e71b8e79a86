from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from typing import TypedDict, List, Annotated
from langgraph.checkpoint.memory import InMemorySaver
from langchain_core.prompts import Chat<PERSON>romptTemplate
from config.llm import llm
from langgraph.checkpoint.postgres import PostgresSaver
from psycopg import Connection
from agents.prompts import ROUTER_PROMPT, RAG_AGENT_PROMPT, SCHEDULE_AGENT_PROMPT, GENERIC_AGENT_PROMPT
from agents.tools import rag_retrieve, create_todo, get_todos, update_todo, delete_todo, tavily_search, query_webapp_database, create_multiple_todos, create_learning_plan, parse_schedule_text, smart_todo_creator
from datetime import datetime
from dotenv import load_dotenv
import os
load_dotenv()

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    route_decision: str
    response: str
    user_context: dict  # Store user preferences, previous requests
    conversation_history: List[dict]  # Store conversation context

def router_node(state: AgentState) -> AgentState:
    """Enhanced router agent with keyword detection and fallback logic."""
    user_input = state["messages"][-1].content.lower()
    messages = state["messages"]

    # Tạo chat history từ tất cả messages
    chat_history = ""
    for msg in messages[-5:]:  # Chỉ lấy 5 tin nhắn gần nhất để tránh quá dài
        role = "User" if isinstance(msg, HumanMessage) else "Assistant"
        chat_history += f"{role}: {msg.content}\n"

    # Keyword-based routing for quick decisions
    rag_keywords = [
        'học phí', 'tuyển sinh', 'nội quy', 'môn học', 'chương trình', 'điểm chuẩn',
        'học bổng', 'khoa', 'ngành', 'trường', 'fpt', 'đại học', 'sinh viên',
        'thi', 'điểm', 'tín chỉ', 'thủ tục', 'hồ sơ', 'xét tuyển'
    ]

    schedule_keywords = [
        'task', 'todo', 'công việc', 'lịch trình', 'nhắc nhở', 'deadline',
        'kế hoạch', 'tạo', 'xóa', 'sửa', 'cập nhật', 'danh sách',
        'hoàn thành', 'làm việc', 'học tập', 'ôn thi', 'bài tập'
    ]

    # Quick keyword matching
    rag_score = sum(1 for keyword in rag_keywords if keyword in user_input)
    schedule_score = sum(1 for keyword in schedule_keywords if keyword in user_input)

    # If clear keyword match, use it
    if rag_score > schedule_score and rag_score > 0:
        route_decision = "rag_agent"
    elif schedule_score > rag_score and schedule_score > 0:
        route_decision = "schedule_agent"
    else:
        # Use LLM for complex cases
        try:
            router_prompt = ChatPromptTemplate.from_template(ROUTER_PROMPT)
            router_chain = router_prompt | llm

            response = router_chain.invoke({
                "user_input": state["messages"][-1].content,
                "chat_history": chat_history
            })

            # Extract the route decision from the response
            route_decision = response.content.strip().lower()

            # Ensure valid route decision
            if "rag_agent" in route_decision:
                route_decision = "rag_agent"
            elif "schedule_agent" in route_decision:
                route_decision = "schedule_agent"
            elif "generic_agent" in route_decision:
                route_decision = "generic_agent"
            else:
                # Default to generic if unclear
                route_decision = "generic_agent"
        except Exception as e:
            print(f"Router LLM error: {e}")
            route_decision = "generic_agent"
    
    return {
        **state,
        "route_decision": route_decision
        #"messages": [AIMessage(content=f"Routing to: {route_decision}")],
    }

def create_rag_agent():
    """Create RAG agent using create_react_agent with enhanced tools."""
    tools = [rag_retrieve, query_webapp_database]
    # Format prompt with current datetime
    current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_prompt = RAG_AGENT_PROMPT.format(current_datetime=current_datetime)
    return create_react_agent(llm, tools, prompt=formatted_prompt)

def create_schedule_agent():
    """Create Schedule agent using create_react_agent."""
    tools = [smart_todo_creator, create_todo, get_todos, update_todo, delete_todo, create_multiple_todos, create_learning_plan, parse_schedule_text]
    # Format prompt with current datetime
    current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_prompt = SCHEDULE_AGENT_PROMPT.format(current_datetime=current_datetime)
    return create_react_agent(llm, tools, prompt=formatted_prompt)

def create_generic_agent():
    """Create Generic agent using create_react_agent."""
    tools = [tavily_search]
    # Format prompt with current datetime
    current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_prompt = GENERIC_AGENT_PROMPT.format(current_datetime=current_datetime)
    return create_react_agent(llm, tools, prompt=formatted_prompt)

# Create agent instances
rag_agent = create_rag_agent()
schedule_agent = create_schedule_agent()
generic_agent = create_generic_agent()

def rag_agent_node(state: AgentState) -> AgentState:
    """RAG agent node for school information queries."""
    # Sử dụng messages từ state để có memory
    result = rag_agent.invoke({"messages": state["messages"]})
    
    final_message = result["messages"][-1].content if result["messages"] else "No response generated."
    
    return {
        **state,
        "response": final_message,
        "messages": [AIMessage(content=final_message)]
    }

def schedule_agent_node(state: AgentState) -> AgentState:
    """Schedule agent node for CRUD operations."""
    # Sử dụng messages từ state để có memory
    result = schedule_agent.invoke({"messages": state["messages"]})
    
    final_message = result["messages"][-1].content if result["messages"] else "No response generated."
    
    return {
        **state,
        "response": final_message,
        "messages": [AIMessage(content=final_message)]
    }

def generic_agent_node(state: AgentState) -> AgentState:
    """Generic agent node for general queries."""
    # Sử dụng messages từ state để có memory
    result = generic_agent.invoke({"messages": state["messages"]})
    
    final_message = result["messages"][-1].content if result["messages"] else "No response generated."
    
    return {
        **state,
        "response": final_message,
        "messages": [AIMessage(content=final_message)]
    }

def route_to_agent(state: AgentState) -> str:
    """Enhanced conditional routing function with logging."""
    route_decision = state.get("route_decision", "generic_agent")
    user_input = state["messages"][-1].content if state["messages"] else "No input"

    # Log routing decision for debugging (safe encoding)
    try:
        safe_input = user_input[:50].encode('ascii', 'ignore').decode('ascii')
        print(f"[ROUTING] Decision: {route_decision} for input: '{safe_input}...'")
    except:
        print(f"[ROUTING] Decision: {route_decision} for input: [non-ascii text]")

    # Validate route decision
    valid_routes = ["rag_agent", "schedule_agent", "generic_agent"]
    if route_decision not in valid_routes:
        print(f"[WARNING] Invalid route '{route_decision}', defaulting to generic_agent")
        route_decision = "generic_agent"

    return route_decision

# Create the graph
def create_graph(checkpointer: PostgresSaver = None) -> StateGraph:
    """Create the multi-agent workflow graph."""
    graph = StateGraph(AgentState)
    
    # Add nodes
    graph.add_node("router", router_node)
    graph.add_node("rag_agent", rag_agent_node)
    graph.add_node("schedule_agent", schedule_agent_node)
    graph.add_node("generic_agent", generic_agent_node)
    
    # Set entry point
    graph.set_entry_point("router")
    
    # Add conditional routing
    graph.add_conditional_edges(
        "router",
        route_to_agent,
        {
            "rag_agent": "rag_agent",
            "schedule_agent": "schedule_agent",
            "generic_agent": "generic_agent"
        }
    )
    
    # Add edges to end
    graph.add_edge("rag_agent", END)
    graph.add_edge("schedule_agent", END)
    graph.add_edge("generic_agent", END)
    
    return graph.compile()

DB_URI = os.getenv("DB_URI")

connection_kwargs = {
    "autocommit": True,
    "prepare_threshold": 0,
    "sslmode": "require",
}

conn = Connection.connect(DB_URI, **connection_kwargs)
checkpointer = PostgresSaver(conn)
#checkpointer.setup()
multi_agent_graph = create_graph(checkpointer)
