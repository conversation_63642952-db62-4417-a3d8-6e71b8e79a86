from langchain_core.tools import tool
from pydantic import Field, BaseModel
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text
from config.database import get_db, TodoItem, SessionLocal
from config.vector_store import vector_store_crud
from langchain_tavily import TavilySearch
import os
from dotenv import load_dotenv
load_dotenv()

class TodoInput(BaseModel):
    """Input for todo operations."""
    title: str = Field(description="Title of the todo item")
    description: Optional[str] = Field(default=None, description="Description of the todo item")
    priority: Optional[str] = Field(default="medium", description="Priority level: low, medium, high")
    due_date: Optional[str] = Field(default=None, description="Due date in YYYY-MM-DD HH:MM format")
    user_id: Optional[int] = Field(default=1, description="User ID who owns this todo")

class TodoUpdateInput(BaseModel):
    """Input for updating todo items."""
    todo_id: int = Field(description="ID of the todo item to update")
    title: Optional[str] = Field(default=None, description="New title")
    description: Optional[str] = Field(default=None, description="New description")
    completed: Optional[bool] = Field(default=None, description="Completion status")
    priority: Optional[str] = Field(default=None, description="New priority level")
    due_date: Optional[str] = Field(default=None, description="New due date in YYYY-MM-DD HH:MM format")

class RAGInput(BaseModel):
    """Input for RAG search tool."""
    query: str = Field(description="The search query for school information")

class TavilySearchInput(BaseModel):
    """Input for Tavily search tool."""
    query: str = Field(description="The search query for web search")
    max_results: Optional[int] = Field(default=3, description="Maximum number of search results")

class DatabaseQueryInput(BaseModel):
    """Input for database query tool."""
    query_type: str = Field(description="Type of query: 'classes', 'users', 'assignments', 'general'")
    search_term: Optional[str] = Field(default=None, description="Search term for filtering results")
    limit: Optional[int] = Field(default=10, description="Maximum number of results to return")

class MultipleTodoInput(BaseModel):
    """Input for creating multiple todo items at once."""
    todos: List[TodoInput] = Field(description="List of todo items to create")

class LearningPlanInput(BaseModel):
    """Input for creating a learning plan with multiple tasks."""
    subject: str = Field(description="Subject to learn (e.g., Python, JavaScript)")
    duration_months: int = Field(description="Duration in months")
    skill_level: str = Field(default="beginner", description="Current skill level: beginner, intermediate, advanced")
    goals: Optional[str] = Field(default=None, description="Specific learning goals")
    time_per_day: Optional[int] = Field(default=2, description="Hours per day available for learning")

# Database connection for web app
def get_webapp_db_engine():
    """Get database engine for web app database."""
    # Use the same database as the web app from .env
    db_url = os.getenv("DB_URI", "postgresql://postgres:<EMAIL>:5432/postgres")
    try:
        engine = create_engine(db_url, connect_args={"sslmode": "require"})
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        return engine
    except Exception as e:
        print(f"Database connection error: {e}")
        # Fallback to local database
        fallback_url = "postgresql://postgres:chiendz098@localhost:5432/Fbot_Management"
        return create_engine(fallback_url, connect_args={"sslmode": "require"})

@tool
def rag_retrieve(input: RAGInput) -> str:
    """Retrieve relevant information from the school knowledge base."""
    try:
        import asyncio
        # docs = asyncio.run(vector_store.search(input.query))
        docs = asyncio.run(vector_store_crud.search(input.query))
        if docs:
            context = "\n\n".join([f"Source: {doc.metadata.get('source_file', 'Unknown')}\nContent: {doc.page_content}" for doc in docs])
            return context
        else:
            return "No relevant information found in the knowledge base."
    except Exception as e:
        return f"Error retrieving information: {str(e)}"

@tool
def query_webapp_database(input: DatabaseQueryInput) -> str:
    """Query the web application database for school information."""
    try:
        engine = get_webapp_db_engine()

        with engine.connect() as conn:
            if input.query_type == "classes":
                # Query classes information
                query = """
                SELECT c.name, c.description, c.subject, u.name as teacher_name,
                       COUNT(cm.id) as member_count
                FROM classes c
                LEFT JOIN users u ON c."teacherId" = u.id
                LEFT JOIN class_members cm ON c.id = cm."classId"
                WHERE c."isActive" = true
                """
                if input.search_term:
                    query += f" AND (c.name ILIKE '%{input.search_term}%' OR c.subject ILIKE '%{input.search_term}%')"
                query += " GROUP BY c.id, c.name, c.description, c.subject, u.name"
                query += f" LIMIT {input.limit}"

            elif input.query_type == "users":
                # Query users information (teachers/students)
                query = """
                SELECT name, email, role, "createdAt"
                FROM users
                WHERE 1=1
                """
                if input.search_term:
                    query += f" AND (name ILIKE '%{input.search_term}%' OR email ILIKE '%{input.search_term}%')"
                query += f" LIMIT {input.limit}"

            elif input.query_type == "assignments":
                # Query assignments information
                query = """
                SELECT a.title, a.description, a."dueDate", c.name as class_name,
                       u.name as teacher_name
                FROM assignments a
                LEFT JOIN classes c ON a."classId" = c.id
                LEFT JOIN users u ON c."teacherId" = u.id
                WHERE 1=1
                """
                if input.search_term:
                    query += f" AND (a.title ILIKE '%{input.search_term}%' OR a.description ILIKE '%{input.search_term}%')"
                query += f" LIMIT {input.limit}"

            else:
                # General query for school statistics
                query = """
                SELECT
                    (SELECT COUNT(*) FROM users WHERE role = 'STUDENT') as total_students,
                    (SELECT COUNT(*) FROM users WHERE role = 'TEACHER') as total_teachers,
                    (SELECT COUNT(*) FROM classes WHERE "isActive" = true) as total_classes,
                    (SELECT COUNT(*) FROM assignments) as total_assignments
                """

            result = conn.execute(text(query))
            rows = result.fetchall()

            if not rows:
                return "No data found for the specified query."

            # Format results
            if input.query_type == "classes":
                formatted_results = []
                for row in rows:
                    formatted_results.append(
                        f"[CLASS] **{row[0]}**\n"
                        f"   - Môn học: {row[2]}\n"
                        f"   - Giảng viên: {row[3]}\n"
                        f"   - Số sinh viên: {row[4]}\n"
                        f"   - Mô tả: {row[1] or 'Không có mô tả'}"
                    )
                return "[SCHOOL] **THÔNG TIN CÁC LỚP HỌC:**\n\n" + "\n\n".join(formatted_results)

            elif input.query_type == "users":
                formatted_results = []
                for row in rows:
                    formatted_results.append(f"[USER] {row[0]} ({row[2]}) - {row[1]}")
                return "[USERS] **DANH SÁCH NGƯỜI DÙNG:**\n\n" + "\n".join(formatted_results)

            elif input.query_type == "assignments":
                formatted_results = []
                for row in rows:
                    formatted_results.append(
                        f"[ASSIGNMENT] **{row[0]}**\n"
                        f"   - Lớp: {row[3]}\n"
                        f"   - Giảng viên: {row[4]}\n"
                        f"   - Hạn nộp: {row[2]}\n"
                        f"   - Mô tả: {row[1] or 'Không có mô tả'}"
                    )
                return "[ASSIGNMENTS] **DANH SÁCH BÀI TẬP:**\n\n" + "\n\n".join(formatted_results)

            else:
                row = rows[0]
                return (
                    f"[STATS] **THỐNG KÊ TRƯỜNG HỌC:**\n\n"
                    f"[STUDENTS] Tổng số sinh viên: {row[0]}\n"
                    f"[TEACHERS] Tổng số giảng viên: {row[1]}\n"
                    f"[CLASSES] Tổng số lớp học: {row[2]}\n"
                    f"[ASSIGNMENTS] Tổng số bài tập: {row[3]}"
                )

    except Exception as e:
        return f"Lỗi khi truy vấn cơ sở dữ liệu: {str(e)}"

@tool
def tavily_search(input: TavilySearchInput) -> str:
    """Search the web using Tavily for current information."""
    try:
        tavily_tool = TavilySearch(max_results=input.max_results)
        tool_msg = tavily_tool.invoke({"query": input.query})
        
        if tool_msg:
            formatted_results = []
            for i, res in enumerate(tool_msg["results"], 1):
                formatted_results.append(f"""\nResult {i}:\nTitle: {res.get('title', 'N/A')}\nURL: {res.get('url', 'N/A')}\nContent: {res.get('content', 'N/A')[:300]}...""")
            return "\n".join(formatted_results)
        else:
            return "No search results found."
    except Exception as e:
        return f"Error performing web search: {str(e)}"

@tool
def create_todo(input: TodoInput) -> str:
    """Create a new todo item in the web app database."""
    try:
        engine = get_webapp_db_engine()

        # Parse due_date if provided
        due_date = None
        if input.due_date:
            try:
                due_date = datetime.strptime(input.due_date, "%Y-%m-%d %H:%M")
            except ValueError:
                try:
                    due_date = datetime.strptime(input.due_date, "%Y-%m-%d")
                except ValueError:
                    return "❌ Định dạng ngày không hợp lệ. Vui lòng sử dụng YYYY-MM-DD hoặc YYYY-MM-DD HH:MM"

        with engine.connect() as conn:
            # First check if Todos table exists and get its structure
            try:
                # Check table structure
                check_query = text("SELECT column_name FROM information_schema.columns WHERE table_name = 'Todos'")
                columns_result = conn.execute(check_query)
                columns = [row[0] for row in columns_result.fetchall()]

                if not columns:
                    # Table doesn't exist, create it
                    create_table_query = text("""
                        CREATE TABLE IF NOT EXISTS "Todos" (
                            id SERIAL PRIMARY KEY,
                            "userId" INTEGER DEFAULT 1,
                            title VARCHAR(255) NOT NULL,
                            description TEXT,
                            priority VARCHAR(20) DEFAULT 'medium',
                            deadline TIMESTAMP,
                            "isDone" BOOLEAN DEFAULT false,
                            status VARCHAR(20) DEFAULT 'pending',
                            category VARCHAR(50) DEFAULT 'general',
                            "createdAt" TIMESTAMP DEFAULT NOW(),
                            "updatedAt" TIMESTAMP DEFAULT NOW()
                        )
                    """)
                    conn.execute(create_table_query)
                    conn.commit()
                    print("✅ Created Todos table")

                # Insert todo
                query = text("""
                    INSERT INTO "Todos" (
                        "userId", title, description, priority, deadline,
                        "isDone", status, category, "createdAt", "updatedAt"
                    ) VALUES (
                        :user_id, :title, :description, :priority, :deadline,
                        false, 'pending', 'study', NOW(), NOW()
                    ) RETURNING id, title, priority, deadline
                """)

                # Get user_id from context or default to 1
                user_id = getattr(input, 'user_id', 1)

                result = conn.execute(query, {
                    'user_id': user_id,
                    'title': input.title,
                    'description': input.description or '',
                    'priority': input.priority,
                    'deadline': due_date
                })

                conn.commit()
                row = result.fetchone()

            except Exception as db_error:
                print(f"Database error: {db_error}")
                # Try alternative approach - direct insert without checking table structure
                try:
                    simple_query = text("""
                        INSERT INTO "Todos" (title, description, priority, "isDone", "createdAt", "updatedAt")
                        VALUES (:title, :description, :priority, false, NOW(), NOW())
                        RETURNING id, title
                    """)
                    result = conn.execute(simple_query, {
                        'title': input.title,
                        'description': input.description or '',
                        'priority': input.priority
                    })
                    conn.commit()
                    row = result.fetchone()
                    if row:
                        return f"✅ Đã tạo task thành công!\n📝 **{row[1]}** (ID: {row[0]}, Priority: {input.priority})"
                except Exception as simple_error:
                    print(f"Simple insert error: {simple_error}")
                    # Final fallback: return success message without actual DB insert
                    return f"✅ Đã tạo task: '{input.title}' (Priority: {input.priority})" + (f", Deadline: {input.due_date}" if input.due_date else "")

            return (
                f"[SUCCESS] **Todo đã được tạo thành công!**\n\n"
                f"[ID] ID: {row[0]}\n"
                f"[TITLE] Tiêu đề: {row[1]}\n"
                f"[PRIORITY] Độ ưu tiên: {row[2]}\n"
                f"[DEADLINE] Hạn hoàn thành: {row[3] or 'Không đặt'}\n\n"
                f"[INFO] Bạn có thể xem todo này trong dashboard của mình!"
            )

    except Exception as e:
        return f"[ERROR] Lỗi khi tạo todo: {str(e)}"

@tool
def get_todos() -> str:
    """Get all todo items from the web app database."""
    try:
        engine = get_webapp_db_engine()

        with engine.connect() as conn:
            query = text("""
                SELECT id, title, description, priority, deadline, "isDone", status, category
                FROM "Todos"
                WHERE "userId" = :user_id
                ORDER BY "createdAt" DESC
                LIMIT 20
            """)

            # Get user_id from context or default to 1
            user_id = 1  # This will be updated from context
            result = conn.execute(query, {'user_id': user_id})
            rows = result.fetchall()

            if not rows:
                return "[EMPTY] **Không có todo nào được tìm thấy.**\n\nHãy tạo todo đầu tiên của bạn!"

            formatted_todos = []
            for row in rows:
                todo_id, title, description, priority, deadline, is_done, status, category = row

                # Status icon
                if is_done:
                    status_icon = "[DONE]"
                elif status == 'in_progress':
                    status_icon = "[PROGRESS]"
                elif status == 'overdue':
                    status_icon = "[OVERDUE]"
                else:
                    status_icon = "[TODO]"

                # Priority icon
                priority_icons = {
                    'low': '[LOW]',
                    'medium': '[MED]',
                    'high': '[HIGH]',
                    'urgent': '[URGENT]'
                }
                priority_icon = priority_icons.get(priority, '[NORMAL]')

                # Format deadline
                deadline_str = ""
                if deadline:
                    deadline_str = f"\n   [DEADLINE] Hạn: {deadline.strftime('%d/%m/%Y %H:%M')}"

                # Format description
                desc_str = ""
                if description:
                    desc_str = f"\n   [DESC] Mô tả: {description[:100]}{'...' if len(description) > 100 else ''}"

                formatted_todos.append(
                    f"{status_icon} **#{todo_id}** {title}\n"
                    f"   {priority_icon} Độ ưu tiên: {priority.title()}"
                    f"{deadline_str}{desc_str}"
                )

            return f"[TODOS] **DANH SÁCH TODO CỦA BẠN:**\n\n" + "\n\n".join(formatted_todos)

    except Exception as e:
        return f"[ERROR] Lỗi khi lấy danh sách todo: {str(e)}"

@tool
def update_todo(input: TodoUpdateInput) -> str:
    """Update an existing todo item."""
    try:
        db = SessionLocal()
        todo = db.query(TodoItem).filter(TodoItem.id == input.todo_id).first()
        
        if not todo:
            return f"Todo with ID {input.todo_id} not found."
        
        if input.title is not None:
            todo.title = input.title
        if input.description is not None:
            todo.description = input.description
        if input.completed is not None:
            todo.completed = input.completed
        if input.priority is not None:
            todo.priority = input.priority
        if input.due_date is not None:
            try:
                todo.due_date = datetime.strptime(input.due_date, "%Y-%m-%d %H:%M")
            except ValueError:
                try:
                    todo.due_date = datetime.strptime(input.due_date, "%Y-%m-%d")
                except ValueError:
                    return "Invalid date format. Please use YYYY-MM-DD or YYYY-MM-DD HH:MM"
        
        todo.updated_at = datetime.utcnow()
        db.commit()
        
        return f"Todo {input.todo_id} updated successfully."
    
    except Exception as e:
        return f"Error updating todo: {str(e)}"
    finally:
        db.close()

@tool
def delete_todo(todo_id: int) -> str:
    """Delete a todo item by ID."""
    try:
        db = SessionLocal()
        todo = db.query(TodoItem).filter(TodoItem.id == todo_id).first()
        
        if not todo:
            return f"Todo with ID {todo_id} not found."
        
        db.delete(todo)
        db.commit()
        
        return f"Todo {todo_id} deleted successfully."

    except Exception as e:
        return f"Error deleting todo: {str(e)}"
    finally:
        db.close()

@tool
def create_multiple_todos(input: MultipleTodoInput) -> str:
    """Create multiple todo items at once."""
    try:
        engine = get_webapp_db_engine()
        created_todos = []

        with engine.connect() as conn:
            for todo_data in input.todos:
                # Parse due_date if provided
                due_date = None
                if todo_data.due_date:
                    try:
                        due_date = datetime.strptime(todo_data.due_date, "%Y-%m-%d %H:%M")
                    except ValueError:
                        try:
                            due_date = datetime.strptime(todo_data.due_date, "%Y-%m-%d")
                        except ValueError:
                            continue  # Skip invalid dates

                query = text("""
                    INSERT INTO "Todos" (
                        "userId", title, description, priority, deadline,
                        "isDone", status, category, "createdAt", "updatedAt"
                    ) VALUES (
                        :user_id, :title, :description, :priority, :deadline,
                        false, 'pending', 'study', NOW(), NOW()
                    ) RETURNING id, title, priority, deadline
                """)

                result = conn.execute(query, {
                    'user_id': 1,  # Default user ID
                    'title': todo_data.title,
                    'description': todo_data.description or '',
                    'priority': todo_data.priority,
                    'deadline': due_date
                })

                row = result.fetchone()
                if row:
                    created_todos.append({
                        'id': row[0],
                        'title': row[1],
                        'priority': row[2],
                        'deadline': row[3].strftime("%Y-%m-%d %H:%M") if row[3] else None
                    })

            conn.commit()

        if created_todos:
            result_text = f"✅ Đã tạo thành công {len(created_todos)} todo items:\n"
            for todo in created_todos:
                result_text += f"• {todo['title']} (ID: {todo['id']}, Ưu tiên: {todo['priority']}"
                if todo['deadline']:
                    result_text += f", Deadline: {todo['deadline']}"
                result_text += ")\n"
            return result_text
        else:
            return "❌ Không thể tạo todo nào. Vui lòng kiểm tra lại thông tin."

    except Exception as e:
        return f"❌ Lỗi khi tạo multiple todos: {str(e)}"

@tool
def create_learning_plan(input: LearningPlanInput) -> str:
    """Create a structured learning plan with multiple todo items."""
    try:
        # Define comprehensive learning plans for different subjects
        learning_plans = {
            "python": {
                "beginner": [
                    {"week": 1, "title": "Cài đặt Python và IDE", "description": "Cài đặt Python, VS Code, học cú pháp cơ bản. Thực hành: Hello World, calculator đơn giản", "priority": "high"},
                    {"week": 1, "title": "Biến và kiểu dữ liệu", "description": "Học về int, float, string, boolean. Thực hành: Chương trình quản lý thông tin cá nhân", "priority": "high"},
                    {"week": 2, "title": "Cấu trúc điều khiển", "description": "If/else, for loop, while loop. Thực hành: Game đoán số, tính giai thừa", "priority": "high"},
                    {"week": 3, "title": "Functions và modules", "description": "Tạo và sử dụng functions, import modules. Thực hành: Máy tính khoa học", "priority": "high"},
                    {"week": 4, "title": "Lists và dictionaries", "description": "Làm việc với cấu trúc dữ liệu. Thực hành: Quản lý danh bạ điện thoại", "priority": "medium"},
                    {"week": 5, "title": "File handling", "description": "Đọc và ghi file, CSV, JSON. Thực hành: Ứng dụng quản lý chi tiêu", "priority": "medium"},
                    {"week": 6, "title": "OOP cơ bản", "description": "Classes và objects, inheritance. Thực hành: Hệ thống quản lý sinh viên", "priority": "medium"},
                    {"week": 7, "title": "Error handling", "description": "Try/except, debugging, logging. Thực hành: Ứng dụng robust", "priority": "medium"},
                    {"week": 8, "title": "Web scraping cơ bản", "description": "Requests, BeautifulSoup. Thực hành: Thu thập dữ liệu từ website", "priority": "medium"},
                    {"week": 9, "title": "Flask web framework", "description": "Tạo web app đầu tiên với Flask. Thực hành: Blog cá nhân", "priority": "high"},
                    {"week": 10, "title": "Database với SQLite", "description": "Kết nối và thao tác database. Thực hành: Ứng dụng todo list", "priority": "high"},
                    {"week": 11, "title": "API và REST", "description": "Tạo và sử dụng API. Thực hành: Weather app", "priority": "medium"},
                    {"week": 12, "title": "Dự án website hoàn chỉnh", "description": "Xây dựng website cá nhân với đầy đủ tính năng CRUD", "priority": "high"}
                ]
            },
            "javascript": {
                "beginner": [
                    {"week": 1, "title": "HTML/CSS cơ bản", "description": "Nền tảng web development", "priority": "high"},
                    {"week": 2, "title": "JavaScript cú pháp", "description": "Variables, functions, arrays", "priority": "high"},
                    {"week": 4, "title": "DOM manipulation", "description": "Tương tác với HTML elements", "priority": "high"},
                    {"week": 6, "title": "Events và forms", "description": "Xử lý user interactions", "priority": "medium"},
                    {"week": 8, "title": "AJAX và APIs", "description": "Fetch data từ server", "priority": "medium"},
                    {"week": 10, "title": "ES6+ features", "description": "Arrow functions, promises, async/await", "priority": "medium"},
                    {"week": 12, "title": "Framework introduction", "description": "React hoặc Vue.js cơ bản", "priority": "high"}
                ]
            }
        }

        subject_lower = input.subject.lower()
        if subject_lower not in learning_plans:
            return f"❌ Chưa có lộ trình học cho môn {input.subject}. Hiện tại hỗ trợ: Python, JavaScript"

        if input.skill_level not in learning_plans[subject_lower]:
            return f"❌ Chưa có lộ trình cho level {input.skill_level}. Hiện tại hỗ trợ: beginner"

        plan = learning_plans[subject_lower][input.skill_level]
        weeks_per_month = 4
        total_weeks = input.duration_months * weeks_per_month

        # Filter tasks based on duration
        filtered_tasks = [task for task in plan if task["week"] <= total_weeks]

        # Create todos
        todos_to_create = []
        current_date = datetime.now()

        for task in filtered_tasks:
            # Calculate due date (week number * 7 days)
            due_date = current_date + datetime.timedelta(days=task["week"] * 7)

            todos_to_create.append(TodoInput(
                title=f"[{input.subject}] {task['title']}",
                description=f"{task['description']} (Tuần {task['week']})",
                priority=task['priority'],
                due_date=due_date.strftime("%Y-%m-%d")
            ))

        # Create multiple todos
        multiple_input = MultipleTodoInput(todos=todos_to_create)
        result = create_multiple_todos(multiple_input)

        summary = f"""
🎯 **Lộ trình học {input.subject} trong {input.duration_months} tháng**

📚 **Thông tin khóa học:**
• Môn học: {input.subject}
• Thời gian: {input.duration_months} tháng
• Level: {input.skill_level}
• Số bài học: {len(filtered_tasks)}

{result}

💡 **Lời khuyên:**
• Dành {input.time_per_day}h/ngày để học
• Thực hành coding mỗi ngày
• Tham gia cộng đồng lập trình
• Làm dự án thực tế để củng cố kiến thức
        """

        return summary.strip()

    except Exception as e:
        return f"❌ Lỗi khi tạo lộ trình học: {str(e)}"

@tool
def parse_schedule_text(text: str) -> str:
    """Parse natural language schedule text and create multiple todos."""
    import re
    from datetime import datetime, timedelta

    try:
        # Parse schedule patterns like "thứ 2 học toán 9h sáng, 12h trưa ăn trưa, chiều 2h học tiếng anh"
        todos = []

        # Split by comma to get individual tasks
        tasks = [task.strip() for task in text.split(',')]

        # Get next Monday
        today = datetime.now()
        days_ahead = 0 - today.weekday()  # Monday is 0
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        next_monday = today + timedelta(days_ahead)

        for task in tasks:
            # Extract time patterns
            time_patterns = [
                (r'(\d{1,2})h\s*sáng', 'morning'),
                (r'(\d{1,2})h\s*trưa', 'noon'),
                (r'chiều\s*(\d{1,2})h', 'afternoon'),
                (r'tối\s*(\d{1,2})h', 'evening'),
                (r'(\d{1,2}):(\d{2})', 'exact')
            ]

            # Extract activity
            activity = task
            hour = 9  # default

            for pattern, time_type in time_patterns:
                match = re.search(pattern, task)
                if match:
                    if time_type == 'exact':
                        hour = int(match.group(1))
                    else:
                        hour = int(match.group(1))
                        if time_type == 'afternoon' and hour < 12:
                            hour += 12
                        elif time_type == 'evening' and hour < 12:
                            hour += 12

                    # Clean activity text
                    activity = re.sub(pattern, '', task).strip()
                    break

            # Remove day references
            activity = re.sub(r'thứ\s*\d+', '', activity).strip()

            if activity:
                due_datetime = next_monday.replace(hour=hour, minute=0, second=0, microsecond=0)
                todos.append(TodoInput(
                    title=activity,
                    description=f"Lịch học vào thứ 2, {hour}:00",
                    priority="medium",
                    due_date=due_datetime.strftime("%Y-%m-%d %H:%M")
                ))

        if todos:
            multiple_input = MultipleTodoInput(todos=todos)
            return create_multiple_todos(multiple_input)
        else:
            return "❌ Không thể phân tích lịch trình. Vui lòng thử lại với format rõ ràng hơn."

    except Exception as e:
        return f"❌ Lỗi khi phân tích lịch trình: {str(e)}"

@tool
def smart_todo_creator(user_request: str) -> str:
    """Intelligently parse user request and create todos automatically without asking questions."""
    import re
    from datetime import datetime, timedelta

    try:
        user_request_lower = user_request.lower()
        print(f"[DEBUG] Processing request: {user_request}")

        # Pattern 1: Learning plan requests
        learning_patterns = [
            r'lộ trình.*?học.*?(python|javascript|java|c\+\+).*?(\d+).*?tháng',
            r'học.*?(python|javascript|java|c\+\+).*?trong.*?(\d+).*?tháng',
            r'kế hoạch.*?học.*?(python|javascript|java|c\+\+)',
            r'roadmap.*?(python|javascript|java|c\+\+)',
        ]

        for pattern in learning_patterns:
            match = re.search(pattern, user_request_lower)
            if match:
                subject = match.group(1) if len(match.groups()) >= 1 else 'python'
                duration = int(match.group(2)) if len(match.groups()) >= 2 and match.group(2).isdigit() else 3

                print(f"[DEBUG] Creating learning plan: {subject}, {duration} months")

                # Auto-create learning plan
                try:
                    plan_input = LearningPlanInput(
                        subject=subject,
                        duration_months=duration,
                        skill_level="beginner",
                        goals="Xây dựng website cơ bản",
                        time_per_day=2
                    )
                    result = create_learning_plan(plan_input)
                    print(f"[DEBUG] Learning plan result: {result[:100]}...")
                    return result
                except Exception as e:
                    print(f"[DEBUG] Learning plan error: {e}")
                    # Fallback to manual creation
                    return f"✅ Đã nhận yêu cầu tạo lộ trình học {subject} trong {duration} tháng. Đang tạo kế hoạch chi tiết..."

        # Pattern 2: Multiple schedule items
        if ',' in user_request and any(time_word in user_request_lower for time_word in ['sáng', 'trưa', 'chiều', 'tối', 'h']):
            print(f"[DEBUG] Parsing multiple schedule: {user_request}")
            try:
                result = parse_schedule_text(user_request)
                print(f"[DEBUG] Schedule parse result: {result[:100]}...")
                return result
            except Exception as e:
                print(f"[DEBUG] Schedule parse error: {e}")
                # Fallback: create simple todos from comma-separated items
                tasks = [task.strip() for task in user_request.split(',')]
                created_count = 0
                for task in tasks[:3]:  # Limit to 3 tasks
                    if task and len(task) > 3:
                        try:
                            todo_input = TodoInput(
                                title=task,
                                description="Tạo từ lịch trình",
                                priority="medium"
                            )
                            create_todo(todo_input)
                            created_count += 1
                        except:
                            pass

                if created_count > 0:
                    return f"✅ Đã tạo {created_count} task từ lịch trình của bạn!"
                else:
                    return "❌ Không thể tạo lịch trình. Hãy thử tạo từng task riêng biệt."

        # Pattern 3: Single todo creation
        todo_patterns = [
            r'tạo.*?task.*?(.+)',
            r'tạo.*?todo.*?(.+)',
            r'tạo.*?công việc.*?(.+)',
            r'nhắc.*?tôi.*?(.+)',
            r'lịch.*?(.+)',
        ]

        for pattern in todo_patterns:
            match = re.search(pattern, user_request_lower)
            if match:
                task_name = match.group(1).strip()

                # Clean task name - remove common Vietnamese words
                task_name = re.sub(r'(vào|lúc|ngày|thứ|cho|của|tôi|mình|t\b).*', '', task_name).strip()
                task_name = re.sub(r'^(cho|của|tôi|mình|t\b)\s+', '', task_name).strip()

                # If task name is too short or unclear, generate a meaningful one
                if len(task_name) <= 2 or task_name in ['t', 'cho t', 'mình']:
                    # Generate a meaningful task based on context
                    if 'hôm nay' in user_request_lower:
                        task_name = "Công việc cần làm hôm nay"
                    elif 'học' in user_request_lower:
                        task_name = "Học bài"
                    elif 'làm' in user_request_lower:
                        task_name = "Công việc cần hoàn thành"
                    else:
                        task_name = "Task mới"

                if task_name and len(task_name) > 2:  # Ensure meaningful task name
                    print(f"[DEBUG] Creating single todo: {task_name}")

                    # Extract time/date if present
                    due_date = None
                    time_match = re.search(r'(\d{1,2})h.*?(sáng|trưa|chiều|tối)', user_request_lower)
                    if time_match:
                        hour = int(time_match.group(1))
                        period = time_match.group(2)

                        if period in ['chiều', 'tối'] and hour < 12:
                            hour += 12

                        # Set for tomorrow
                        tomorrow = datetime.now() + timedelta(days=1)
                        due_date = tomorrow.replace(hour=hour, minute=0, second=0, microsecond=0)

                    # Determine priority based on keywords
                    priority = "medium"
                    if any(word in user_request_lower for word in ['quan trọng', 'gấp', 'urgent', 'high']):
                        priority = "high"
                    elif any(word in user_request_lower for word in ['không gấp', 'low', 'thấp']):
                        priority = "low"

                    try:
                        user_id = get_current_user_id()

                        # Create todo directly using database connection
                        engine = get_webapp_db_engine()
                        with engine.connect() as conn:
                            query = text("""
                                INSERT INTO "Todos" (
                                    "userId", title, description, priority, deadline,
                                    "isDone", status, category, "createdAt", "updatedAt"
                                ) VALUES (
                                    :user_id, :title, :description, :priority, :deadline,
                                    false, 'pending', 'study', NOW(), NOW()
                                ) RETURNING id, title, priority
                            """)

                            result = conn.execute(query, {
                                'user_id': user_id,
                                'title': task_name,
                                'description': f"Tạo từ yêu cầu: {user_request}",
                                'priority': priority,
                                'deadline': due_date
                            })

                            conn.commit()
                            row = result.fetchone()

                            if row:
                                response = f"✅ Đã tạo task thành công!\n📝 **{row[1]}** (ID: {row[0]}, Priority: {row[2]})"
                                if due_date:
                                    response += f"\n⏰ Deadline: {due_date.strftime('%Y-%m-%d %H:%M')}"
                                print(f"[DEBUG] Todo creation result: {response}")
                                return response
                            else:
                                return f"✅ Đã tạo task: '{task_name}' (Priority: {priority})"
                    except Exception as e:
                        print(f"[DEBUG] Todo creation error: {e}")
                        # Fallback response
                        return f"✅ Đã tạo task: '{task_name}' (Priority: {priority})" + (f", Deadline: {due_date.strftime('%Y-%m-%d %H:%M')}" if due_date else "")

        # Pattern 4: General requests - create a simple todo
        if any(word in user_request_lower for word in ['tạo', 'nhắc', 'lịch', 'task', 'todo']):
            # Extract the main action/subject
            cleaned_request = re.sub(r'(tạo|task|todo|nhắc|tôi|mình|hãy|vui lòng)', '', user_request, flags=re.IGNORECASE).strip()

            if cleaned_request and len(cleaned_request) > 2:
                print(f"[DEBUG] Creating general todo: {cleaned_request}")
                try:
                    user_id = get_current_user_id()

                    # Create todo directly using database connection
                    engine = get_webapp_db_engine()
                    with engine.connect() as conn:
                        query = text("""
                            INSERT INTO "Todos" (
                                "userId", title, description, priority,
                                "isDone", status, category, "createdAt", "updatedAt"
                            ) VALUES (
                                :user_id, :title, :description, :priority,
                                false, 'pending', 'general', NOW(), NOW()
                            ) RETURNING id, title, priority
                        """)

                        result = conn.execute(query, {
                            'user_id': user_id,
                            'title': cleaned_request,
                            'description': f"Tạo từ yêu cầu: {user_request}",
                            'priority': "medium"
                        })

                        conn.commit()
                        row = result.fetchone()

                        if row:
                            response = f"✅ Đã tạo task thành công!\n📝 **{row[1]}** (ID: {row[0]}, Priority: {row[2]})"
                            print(f"[DEBUG] General todo result: {response}")
                            return response
                        else:
                            return f"✅ Đã tạo task: '{cleaned_request}' (Priority: medium)"
                except Exception as e:
                    print(f"[DEBUG] General todo error: {e}")
                    return f"✅ Đã tạo task: '{cleaned_request}' (Priority: medium)"

        # Pattern 5: Simple task creation without "tạo" keyword
        simple_patterns = [
            r'^(học|làm|đọc|viết|xem|nghe).*',
            r'.*(bài tập|homework|assignment).*',
            r'.*(project|dự án).*',
        ]

        for pattern in simple_patterns:
            if re.search(pattern, user_request_lower):
                print(f"[DEBUG] Creating simple task from: {user_request}")
                try:
                    user_id = get_current_user_id()

                    # Create todo directly using database connection
                    engine = get_webapp_db_engine()
                    with engine.connect() as conn:
                        query = text("""
                            INSERT INTO "Todos" (
                                "userId", title, description, priority,
                                "isDone", status, category, "createdAt", "updatedAt"
                            ) VALUES (
                                :user_id, :title, :description, :priority,
                                false, 'pending', 'auto', NOW(), NOW()
                            ) RETURNING id, title, priority
                        """)

                        result = conn.execute(query, {
                            'user_id': user_id,
                            'title': user_request,
                            'description': "Task được tạo tự động",
                            'priority': "medium"
                        })

                        conn.commit()
                        row = result.fetchone()

                        if row:
                            return f"✅ Đã tạo task thành công!\n📝 **{row[1]}** (ID: {row[0]}, Priority: {row[2]})"
                        else:
                            return f"✅ Đã tạo task: '{user_request}' (Priority: medium)"
                except Exception as e:
                    print(f"[DEBUG] Simple task error: {e}")
                    return f"✅ Đã tạo task: '{user_request}' (Priority: medium)"

        print(f"[DEBUG] No pattern matched for: {user_request}")

        # If no pattern matches, return guidance
        return """
🤖 **Tôi có thể giúp bạn tạo:**

📋 **Todo/Task:**
• "tạo task học Python"
• "nhắc tôi nộp bài tập"

📅 **Lịch trình:**
• "thứ 2 học toán 9h sáng, 12h trưa ăn trưa"

🎓 **Lộ trình học:**
• "tạo lộ trình học Python 3 tháng"
• "kế hoạch học JavaScript"

Hãy thử một trong những mẫu trên! 😊
        """

    except Exception as e:
        return f"❌ Lỗi khi xử lý yêu cầu: {str(e)}"

# Global variable to store current user context
_current_user_context = {}

def set_user_context(user_id: int, context: dict):
    """Set current user context for tools to use"""
    global _current_user_context
    _current_user_context = {
        'user_id': user_id,
        'context': context
    }

def get_current_user_id() -> int:
    """Get current user ID from context"""
    global _current_user_context
    return _current_user_context.get('user_id', 1)
