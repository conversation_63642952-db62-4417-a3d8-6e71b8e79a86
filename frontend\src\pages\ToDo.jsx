import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../App';
import Header from '../components/layout/Header';

import { getTodos, addTodo, deleteTodo, updateTodo } from '../api';
import AdvancedTodoCard from '../components/todo/AdvancedTodoCard';
import TodoControls from '../components/todo/TodoControls';

export default function ToDo() {
  const { user } = useAuth();
  const [todos, setTodos] = useState([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('medium');
  const [dueDate, setDueDate] = useState('');
  const [todoType, setTodoType] = useState('normal');
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('createdAt');
  const [showAddForm, setShowAddForm] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // grid, list, kanban

  const fetchTodos = async () => {
    try {
      setLoading(true);
      const res = await getTodos();
      setTodos(res.data || []);
    } catch (error) {
      console.error('Error fetching todos:', error);
      setTodos([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTodos();
  }, []);

  const handleAdd = async (e) => {
    e.preventDefault();
    if (!title.trim()) return;

    try {
      await addTodo({
        title: title.trim(),
        description: description.trim(),
        deadline: dueDate || null,
        type: todoType
      });
      setTitle('');
      setDescription('');
      setPriority('medium');
      setDueDate('');
      setTodoType('normal');
      setShowAddForm(false);
      fetchTodos();
    } catch (error) {
      console.error('Error adding todo:', error);
    }
  };

  const handleToggleComplete = async (todo) => {
    try {
      await updateTodo(todo.id, { isDone: !todo.isDone });
      fetchTodos();
    } catch (error) {
      console.error('Error updating todo:', error);
    }
  };

  const handleUpdate = async (id, data) => {
    try {
      await updateTodo(id, data);
      fetchTodos();
    } catch (error) {
      console.error('Error updating todo:', error);
    }
  };

  const filteredTodos = todos.filter(todo => {
    if (filter === 'all') return true;
    if (filter === 'completed') return todo.isDone;
    if (filter === 'pending') return !todo.isDone;
    if (filter === 'high') return todo.priority === 'high';
    return true;
  });

  const sortedTodos = [...filteredTodos].sort((a, b) => {
    switch (sortBy) {
      case 'deadline':
        if (!a.deadline && !b.deadline) return 0;
        if (!a.deadline) return 1;
        if (!b.deadline) return -1;
        return new Date(a.deadline) - new Date(b.deadline);
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
      case 'title':
        return a.title.localeCompare(b.title);
      default:
        return new Date(b.createdAt) - new Date(a.createdAt);
    }
  });

  const handleDelete = async (id) => {
    try {
      await deleteTodo(id);
      fetchTodos();
    } catch (error) {
      console.error('Error deleting todo:', error);
    }
  };







  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Header />

      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Advanced Controls */}
          <TodoControls
            filter={filter}
            setFilter={setFilter}
            sortBy={sortBy}
            setSortBy={setSortBy}
            viewMode={viewMode}
            setViewMode={setViewMode}
            showAddForm={showAddForm}
            setShowAddForm={setShowAddForm}
            todos={todos}
          />







          {/* Advanced Todo Grid */}
          <motion.div
            className={`
              ${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : ''}
              ${viewMode === 'list' ? 'space-y-4' : ''}
              ${viewMode === 'kanban' ? 'grid grid-cols-1 md:grid-cols-3 gap-6' : ''}
            `}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <AnimatePresence>
              {sortedTodos.map((todo) => (
                <AdvancedTodoCard
                  key={todo._id || todo.id}
                  todo={todo}
                  onToggleComplete={handleToggleComplete}
                  onDelete={handleDelete}
                  onUpdate={handleUpdate}
                />
              ))}
            </AnimatePresence>
          </motion.div>


          {sortedTodos.length === 0 && (
            <motion.div
              className="text-center py-12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {filter === 'completed' ? 'Chưa có công việc hoàn thành' :
                 filter === 'pending' ? 'Chưa có công việc đang chờ' :
                 'Chưa có công việc nào'}
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {filter === 'all' ? 'Thêm công việc đầu tiên để bắt đầu quản lý' : 'Thử thay đổi bộ lọc để xem công việc khác'}
              </p>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}