"""
Memory system for chatbot to remember user context and preferences
"""

from typing import Dict, List, Any
import json
import os
from datetime import datetime

class ChatMemory:
    """Simple in-memory storage for user context and conversation history"""
    
    def __init__(self):
        self.user_contexts = {}  # user_id -> context
        self.conversation_histories = {}  # user_id -> history
        self.memory_file = "chatbot_memory.json"
        self.load_memory()
    
    def load_memory(self):
        """Load memory from file"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.user_contexts = data.get('user_contexts', {})
                    self.conversation_histories = data.get('conversation_histories', {})
        except Exception as e:
            print(f"Error loading memory: {e}")
    
    def save_memory(self):
        """Save memory to file"""
        try:
            data = {
                'user_contexts': self.user_contexts,
                'conversation_histories': self.conversation_histories
            }
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving memory: {e}")
    
    def get_user_context(self, user_id: str = "default") -> Dict[str, Any]:
        """Get user context"""
        return self.user_contexts.get(user_id, {
            'preferences': {},
            'learning_goals': [],
            'skill_level': 'beginner',
            'created_todos': [],
            'last_interaction': None
        })
    
    def update_user_context(self, user_id: str = "default", **kwargs):
        """Update user context"""
        if user_id not in self.user_contexts:
            self.user_contexts[user_id] = self.get_user_context(user_id)
        
        self.user_contexts[user_id].update(kwargs)
        self.user_contexts[user_id]['last_interaction'] = datetime.now().isoformat()
        self.save_memory()
    
    def add_to_conversation_history(self, user_id: str = "default", user_message: str = "", bot_response: str = ""):
        """Add conversation to history"""
        if user_id not in self.conversation_histories:
            self.conversation_histories[user_id] = []
        
        conversation_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'bot_response': bot_response
        }
        
        self.conversation_histories[user_id].append(conversation_entry)
        
        # Keep only last 50 conversations
        if len(self.conversation_histories[user_id]) > 50:
            self.conversation_histories[user_id] = self.conversation_histories[user_id][-50:]
        
        self.save_memory()
    
    def get_conversation_history(self, user_id: str = "default", limit: int = 10) -> List[Dict]:
        """Get recent conversation history"""
        history = self.conversation_histories.get(user_id, [])
        return history[-limit:] if history else []
    
    def extract_user_preferences(self, user_message: str, user_id: str = "default"):
        """Extract and store user preferences from message"""
        message_lower = user_message.lower()
        context = self.get_user_context(user_id)
        
        # Extract skill level
        if any(word in message_lower for word in ['beginner', 'mới bắt đầu', 'chưa biết gì']):
            context['skill_level'] = 'beginner'
        elif any(word in message_lower for word in ['intermediate', 'trung bình', 'biết chút ít']):
            context['skill_level'] = 'intermediate'
        elif any(word in message_lower for word in ['advanced', 'nâng cao', 'đã biết nhiều']):
            context['skill_level'] = 'advanced'
        
        # Extract learning goals
        if 'website' in message_lower:
            if 'website' not in context['learning_goals']:
                context['learning_goals'].append('website development')
        
        if any(lang in message_lower for lang in ['python', 'javascript', 'java', 'c++']):
            for lang in ['python', 'javascript', 'java', 'c++']:
                if lang in message_lower and lang not in context['learning_goals']:
                    context['learning_goals'].append(f'{lang} programming')
        
        # Extract time preferences
        if 'cuối tuần' in message_lower:
            context['preferences']['study_time'] = 'weekends'
        
        if any(time in message_lower for time in ['sáng', 'trưa', 'chiều', 'tối']):
            for time in ['sáng', 'trưa', 'chiều', 'tối']:
                if time in message_lower:
                    context['preferences']['preferred_time'] = time
                    break
        
        self.update_user_context(user_id, **context)
    
    def get_context_summary(self, user_id: str = "default") -> str:
        """Get a summary of user context for the AI"""
        context = self.get_user_context(user_id)
        recent_history = self.get_conversation_history(user_id, 3)
        
        summary = f"""
User Context:
- Skill Level: {context.get('skill_level', 'unknown')}
- Learning Goals: {', '.join(context.get('learning_goals', []))}
- Preferences: {context.get('preferences', {})}
- Created Todos: {len(context.get('created_todos', []))} tasks

Recent Conversations:
"""
        
        for conv in recent_history:
            summary += f"- User: {conv['user_message'][:50]}...\n"
            summary += f"  Bot: {conv['bot_response'][:50]}...\n"
        
        return summary

# Global memory instance
chat_memory = ChatMemory()
