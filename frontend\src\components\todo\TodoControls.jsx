import React from 'react';
import { motion } from 'framer-motion';

const TodoControls = ({ 
  filter, 
  setFilter, 
  sortBy, 
  setSortBy, 
  viewMode, 
  setViewMode,
  showAddForm,
  setShowAddForm,
  todos 
}) => {
  const filterOptions = [
    { value: 'all', label: 'Tất cả', count: todos.length },
    { value: 'pending', label: 'Chưa xong', count: todos.filter(t => !t.isDone).length },
    { value: 'completed', label: 'Hoàn thành', count: todos.filter(t => t.isDone).length },
    { value: 'high', label: 'Ưu tiên cao', count: todos.filter(t => t.priority === 'high').length },
  ];

  const sortOptions = [
    { value: 'createdAt', label: 'Mới nhất', icon: '🕒' },
    { value: 'deadline', label: 'Deadline', icon: '⏰' },
    { value: 'priority', label: 'Ưu tiên', icon: '🎯' },
    { value: 'title', label: 'Tên A-Z', icon: '🔤' },
  ];

  const viewOptions = [
    { value: 'grid', label: 'Lưới', icon: '⊞' },
    { value: 'list', label: 'Danh sách', icon: '☰' },
    { value: 'kanban', label: 'Kanban', icon: '📋' },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">📋 Todo List AI</h2>
          <p className="text-gray-600">Quản lý công việc thông minh với AI</p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setShowAddForm(!showAddForm)}
          className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          ✨ Thêm Task
        </motion.button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        {filterOptions.map((option) => (
          <motion.div
            key={option.value}
            whileHover={{ scale: 1.02 }}
            className={`
              p-4 rounded-lg border-2 cursor-pointer transition-all duration-300
              ${filter === option.value 
                ? 'border-blue-500 bg-blue-50 text-blue-700' 
                : 'border-gray-200 bg-gray-50 hover:border-gray-300'
              }
            `}
            onClick={() => setFilter(option.value)}
          >
            <div className="text-2xl font-bold">{option.count}</div>
            <div className="text-sm">{option.label}</div>
          </motion.div>
        ))}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        {/* Sort */}
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700">Sắp xếp:</span>
          <div className="flex space-x-2">
            {sortOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setSortBy(option.value)}
                className={`
                  px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300
                  ${sortBy === option.value
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                {option.icon} {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* View Mode */}
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700">Hiển thị:</span>
          <div className="flex space-x-2">
            {viewOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setViewMode(option.value)}
                className={`
                  px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300
                  ${viewMode === option.value
                    ? 'bg-purple-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                {option.icon} {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* AI Suggestions */}
      <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-lg">🤖</span>
          <span className="font-semibold text-green-700">AI Gợi ý</span>
        </div>
        <p className="text-sm text-green-600">
          Thử nói với chatbot: <strong>"tạo task học Python deadline 5h chiều ưu tiên cao"</strong> 
          để tạo todo thông minh với đầy đủ thông tin!
        </p>
      </div>
    </div>
  );
};

export default TodoControls;
