const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { Todo, User } = require('../models');
const axios = require('axios');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const config = require('../config');

// Middleware xác thực JWT
function auth(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) return res.status(401).json({ message: 'No token' });
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET);
    req.userId = decoded.id;
    next();
  } catch {
    res.status(401).json({ message: 'Invalid token' });
  }
}

// Khởi tạo Google AI
const genAI = new GoogleGenerativeAI(config.GOOGLE_API_KEY);

// Router function để quyết định agent nào xử lý
async function routeToAgent(userInput, chatHistory = '') {
  const ragKeywords = [
    'học phí', 'tuyển sinh', 'nội quy', 'môn học', 'chương trình', 'điểm chuẩn',
    'học bổng', 'khoa', 'ngành', 'trường', 'fpt', 'đại học', 'sinh viên',
    'thi', 'điểm', 'tín chỉ', 'thủ tục', 'hồ sơ', 'xét tuyển'
  ];

  const scheduleKeywords = [
    'task', 'todo', 'công việc', 'lịch trình', 'nhắc nhở', 'deadline',
    'kế hoạch', 'tạo', 'xóa', 'sửa', 'cập nhật', 'danh sách',
    'hoàn thành', 'làm việc', 'học tập', 'ôn thi', 'bài tập'
  ];

  const userInputLower = userInput.toLowerCase();
  
  // Quick keyword matching
  const ragScore = ragKeywords.filter(keyword => userInputLower.includes(keyword)).length;
  const scheduleScore = scheduleKeywords.filter(keyword => userInputLower.includes(keyword)).length;

  if (ragScore > scheduleScore && ragScore > 0) {
    return 'rag_agent';
  } else if (scheduleScore > ragScore && scheduleScore > 0) {
    return 'schedule_agent';
  } else {
    // Use LLM for complex cases
    try {
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      const prompt = `Dựa trên câu hỏi của người dùng, hãy quyết định agent nào sẽ xử lý:
      
Câu hỏi: "${userInput}"
Lịch sử chat: ${chatHistory}

Các agent có sẵn:
- rag_agent: Cho câu hỏi về thông tin trường học, học phí, tuyển sinh, nội quy, môn học
- schedule_agent: Cho câu hỏi về quản lý task, todo, lịch trình, nhắc nhở
- generic_agent: Cho câu hỏi chung, tìm kiếm thông tin

Chỉ trả lời tên agent (rag_agent, schedule_agent, hoặc generic_agent):`;

      // Deprecated: Using Python chatbot instead
      console.log('⚠️ LLM routing deprecated - using Python chatbot');
      return 'python_chatbot';
    } catch (error) {
      console.error('Router error:', error);
      return 'python_chatbot';
    }
  }
}

// RAG Agent - DEPRECATED: Now using Python chatbot
async function ragAgent(userInput, userId) {
  console.log('⚠️ ragAgent called but deprecated - using Python chatbot instead');
  return 'Chức năng này đã được chuyển sang Python chatbot. Vui lòng thử lại.';
  
  const prompt = `Bạn là trợ lý AI của trường Đại học FPT. Hãy trả lời câu hỏi về thông tin trường học một cách chính xác và hữu ích.

Thông tin cơ bản về FPT University:
- Địa chỉ: Lô E2a-7, Đường D1, Đ. D1, Long Thạnh Mỹ, Thành Phố Thủ Đức, Thành phố Hồ Chí Minh
- Website: https://fpt.edu.vn
- Điện thoại: (028) 7300 1866
- Email: <EMAIL>

Các ngành đào tạo chính:
- Công nghệ thông tin (CNTT)
- Kinh doanh quốc tế
- Quản trị kinh doanh
- Marketing
- Ngôn ngữ Anh
- Thiết kế đồ họa

Học phí (tham khảo):
- CNTT: ~25-30 triệu/năm
- Các ngành khác: ~20-25 triệu/năm

Câu hỏi: "${userInput}"

Hãy trả lời bằng tiếng Việt một cách thân thiện và hữu ích:`;

  try {
    const result = await model.generateContent(prompt);
    return result.response.text();
  } catch (error) {
    return 'Xin lỗi, tôi không thể trả lời câu hỏi này lúc này. Vui lòng thử lại sau.';
  }
}

// Schedule Agent - DEPRECATED: Now using Python chatbot
async function scheduleAgent(userInput, userId) {
  console.log('⚠️ scheduleAgent called but deprecated - using Python chatbot instead');
  return 'Chức năng này đã được chuyển sang Python chatbot. Vui lòng thử lại.';
  
  // Phân tích intent từ user input
  const createTodoMatch = userInput.match(/tạo\s+(?:task|todo|nhắc nhở|công việc)\s+(.+)/i);
  const listTodoMatch = userInput.match(/xem\s+(?:danh sách\s+)?(?:task|todo|nhắc nhở|công việc)/i);
  const updateTodoMatch = userInput.match(/(?:hoàn thành|đánh dấu|mark)\s+(.+)/i);
  const deleteTodoMatch = userInput.match(/xóa\s+(?:task|todo|nhắc nhở|công việc)\s+(.+)/i);

  try {
    if (createTodoMatch) {
      const title = createTodoMatch[1].trim();
      const todo = await Todo.create({ userId: userId, title });
      return `✅ Đã tạo task: "${title}"`;
    } else if (listTodoMatch) {
      const todos = await Todo.findAll({ where: { userId: userId } });
      if (todos.length === 0) {
        return '📝 Bạn chưa có task nào. Hãy tạo task mới!';
      }
      const todoList = todos.map((todo, index) => 
        `${index + 1}. ${todo.title} ${todo.isDone ? '✅' : '⏳'}`
      ).join('\n');
      return `📋 Danh sách task của bạn:\n${todoList}`;
    } else if (updateTodoMatch) {
      const title = updateTodoMatch[1].trim();
      const todo = await Todo.findOne({
        where: { userId: userId, title: title }
      });
      if (todo) {
        await todo.update({ isDone: true });
        return `✅ Đã hoàn thành task: "${title}"`;
      } else {
        return `❌ Không tìm thấy task: "${title}"`;
      }
    } else if (deleteTodoMatch) {
      const title = deleteTodoMatch[1].trim();
      const todo = await Todo.findOne({
        where: { userId: userId, title: title }
      });
      if (todo) {
        await todo.destroy();
        return `🗑️ Đã xóa task: "${title}"`;
      } else {
        return `❌ Không tìm thấy task: "${title}"`;
      }
    } else {
      // Sử dụng LLM để trả lời chung về quản lý task
      const prompt = `Bạn là trợ lý quản lý task. Người dùng hỏi: "${userInput}"

Hãy trả lời về cách quản lý task, todo list, lịch trình. Bạn có thể:
- Tạo task mới: "tạo task [tên task]"
- Xem danh sách: "xem task"
- Hoàn thành task: "hoàn thành [tên task]"
- Xóa task: "xóa task [tên task]"

Trả lời bằng tiếng Việt:`;

      const result = await model.generateContent(prompt);
      return result.response.text();
    }
  } catch (error) {
    console.error('Schedule agent error:', error);
    return 'Xin lỗi, có lỗi xảy ra khi xử lý task. Vui lòng thử lại.';
  }
}

// Generic Agent - DEPRECATED: Now using Python chatbot
async function genericAgent(userInput) {
  console.log('⚠️ genericAgent called but deprecated - using Python chatbot instead');
  return 'Chức năng này đã được chuyển sang Python chatbot. Vui lòng thử lại.';
  
  const prompt = `Bạn là trợ lý AI thông minh. Hãy trả lời câu hỏi của người dùng một cách hữu ích và thân thiện.

Câu hỏi: "${userInput}"

Hãy trả lời bằng tiếng Việt:`;

  try {
    const result = await model.generateContent(prompt);
    return result.response.text();
  } catch (error) {
    return 'Xin lỗi, tôi không thể trả lời câu hỏi này lúc này. Vui lòng thử lại sau.';
  }
}

// Main chatbot endpoint - now uses Python chatbot
router.post('/chat', auth, async (req, res) => {
  const { message, chatHistory = [] } = req.body;
  const userId = req.userId;

  if (!message || !message.trim()) {
    return res.status(400).json({ error: 'Message is required' });
  }

  try {
    // Call Python chatbot directly
    const axios = require('axios');
    const CHATBOT_URL = 'http://localhost:8002';

    console.log(`🤖 Sending to Python chatbot: "${message}" for user ${userId}`);

    const response = await axios.post(`${CHATBOT_URL}/chat`, {
      message: message,
      user_id: userId,
      chat_history: chatHistory
    }, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const chatbotResponse = response.data.response || 'Xin lỗi, tôi không thể trả lời câu hỏi này.';

    res.json({
      response: chatbotResponse,
      agentType: 'python_chatbot',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Python chatbot error:', error.message);

    // Fallback response
    res.json({
      response: 'Xin lỗi, chatbot hiện tại không khả dụng. Vui lòng thử lại sau.',
      agentType: 'fallback',
      timestamp: new Date().toISOString()
    });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    // Check Python chatbot health
    const axios = require('axios');
    const CHATBOT_URL = 'http://localhost:8002';

    const response = await axios.get(`${CHATBOT_URL}/health`, { timeout: 5000 });

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'Chatbot service is running',
      pythonChatbot: {
        status: 'healthy',
        url: CHATBOT_URL
      }
    });
  } catch (error) {
    res.json({
      status: 'partial',
      timestamp: new Date().toISOString(),
      message: 'Node.js service is running, Python chatbot unavailable',
      pythonChatbot: {
        status: 'unhealthy',
        error: error.message
      }
    });
  }
});

module.exports = router; 